import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { determineTaskChatMembers, getTaskChatContext, updateTaskChatMembership } from '@/utils/chatMembershipUtils';

interface TestScenario {
  id: string;
  name: string;
  description: string;
  setup: () => Promise<{ taskId: string; expectedMembers: string[]; expectedScenario: string }>;
  status: 'pending' | 'running' | 'passed' | 'failed';
  result?: any;
  error?: string;
}

const InternalTaskChatTester = () => {
  const { user, profile } = useAuth();

  // Debug organization info
  console.log('[InternalTaskChatTester] User profile:', {
    userId: user?.id,
    organizationId: profile?.organization_id,
    role: profile?.role,
    accountType: profile?.account_type
  });
  const [scenarios, setScenarios] = useState<TestScenario[]>([
    {
      id: 'admin_self_assign',
      name: 'Admin Self-Assignment',
      description: 'Admin creates task and assigns to themselves',
      setup: async () => {
        // Create a task as admin
        const { data: task, error } = await supabase
          .from('tasks')
          .insert({
            title: `Test Admin Self-Assign ${Date.now()}`,
            description: 'Test task for admin self-assignment',
            category: 'Maintenance',
            budget: 100,
            due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            location: 'Test Location',
            visibility: 'internal',
            status: 'open',
            user_id: user!.id,
            assigned_to: user!.id // Self-assign
          })
          .select()
          .single();

        if (error) throw error;

        return {
          taskId: task.id,
          expectedMembers: [user!.id], // Only admin
          expectedScenario: 'admin_self_assigned'
        };
      },
      status: 'pending'
    },
    {
      id: 'admin_assign_to_staff',
      name: 'Admin Assigns to Staff',
      description: 'Admin creates task and assigns to maintenance staff',
      setup: async () => {
        // Find a maintenance staff member
        const { data: staffMember } = await supabase
          .from('profiles')
          .select('id')
          .eq('organization_id', profile?.organization_id)
          .eq('role', 'maintenance')
          .limit(1)
          .single();

        if (!staffMember) throw new Error('No maintenance staff found');

        // Create a task as admin
        const { data: task, error } = await supabase
          .from('tasks')
          .insert({
            title: `Test Admin Assign to Staff ${Date.now()}`,
            description: 'Test task for admin assigning to staff',
            category: 'Maintenance',
            budget: 100,
            due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            location: 'Test Location',
            visibility: 'internal',
            status: 'open',
            user_id: user!.id,
            assigned_to: staffMember.id
          })
          .select()
          .single();

        if (error) throw error;

        return {
          taskId: task.id,
          expectedMembers: [user!.id, staffMember.id], // Admin + staff
          expectedScenario: 'admin_assigned_to_staff'
        };
      },
      status: 'pending'
    },
    {
      id: 'teacher_created_pending',
      name: 'Teacher Created (Pending)',
      description: 'Teacher creates task, awaiting admin review',
      setup: async () => {
        // Find a teacher
        const { data: teacher } = await supabase
          .from('profiles')
          .select('id')
          .eq('organization_id', profile?.organization_id)
          .eq('role', 'teacher')
          .limit(1)
          .single();

        if (!teacher) throw new Error('No teacher found');

        // Create a task as teacher (unassigned)
        const { data: task, error } = await supabase
          .from('tasks')
          .insert({
            title: `Test Teacher Created ${Date.now()}`,
            description: 'Test task created by teacher',
            category: 'Maintenance',
            budget: 100,
            due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            location: 'Test Location',
            visibility: 'internal',
            status: 'open',
            user_id: teacher.id
            // No assigned_to - pending review
          })
          .select()
          .single();

        if (error) throw error;

        return {
          taskId: task.id,
          expectedMembers: [teacher.id], // Only teacher
          expectedScenario: 'teacher_created_pending_review'
        };
      },
      status: 'pending'
    },
    {
      id: 'teacher_created_admin_assigned',
      name: 'Teacher Created, Admin Assigned to Staff',
      description: 'Teacher creates task, admin assigns to maintenance staff',
      setup: async () => {
        // Find a teacher and staff member
        const { data: teacher } = await supabase
          .from('profiles')
          .select('id')
          .eq('organization_id', profile?.organization_id)
          .eq('role', 'teacher')
          .limit(1)
          .single();

        const { data: staffMember } = await supabase
          .from('profiles')
          .select('id')
          .eq('organization_id', profile?.organization_id)
          .eq('role', 'maintenance')
          .limit(1)
          .single();

        if (!teacher || !staffMember) throw new Error('Teacher or staff member not found');

        // Create a task as teacher, then assign as admin
        const { data: task, error } = await supabase
          .from('tasks')
          .insert({
            title: `Test Teacher Created Admin Assigned ${Date.now()}`,
            description: 'Test task created by teacher, assigned by admin',
            category: 'Maintenance',
            budget: 100,
            due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            location: 'Test Location',
            visibility: 'internal',
            status: 'open',
            user_id: teacher.id,
            assigned_to: staffMember.id
          })
          .select()
          .single();

        if (error) throw error;

        return {
          taskId: task.id,
          expectedMembers: [teacher.id, user!.id, staffMember.id], // Teacher + Admin + Staff
          expectedScenario: 'teacher_created_admin_assigned_to_staff'
        };
      },
      status: 'pending'
    }
  ]);

  const runScenario = async (scenarioId: string) => {
    setScenarios(prev => prev.map(s =>
      s.id === scenarioId ? { ...s, status: 'running' } : s
    ));

    try {
      const scenario = scenarios.find(s => s.id === scenarioId);
      if (!scenario) throw new Error('Scenario not found');

      // Setup the scenario
      const { taskId, expectedMembers, expectedScenario } = await scenario.setup();

      // Get the task context
      const context = await getTaskChatContext(taskId);
      if (!context) throw new Error('Failed to get task context');

      // Determine chat members
      const result = await determineTaskChatMembers(context);

      // Update chat membership
      await updateTaskChatMembership(taskId);

      // Validate results
      const membersMatch = JSON.stringify(result.members.sort()) === JSON.stringify(expectedMembers.sort());
      const scenarioMatch = result.scenario === expectedScenario;

      if (membersMatch && scenarioMatch) {
        setScenarios(prev => prev.map(s =>
          s.id === scenarioId ? {
            ...s,
            status: 'passed',
            result: {
              taskId,
              actualMembers: result.members,
              expectedMembers,
              actualScenario: result.scenario,
              expectedScenario,
              description: result.description
            }
          } : s
        ));
      } else {
        throw new Error(`Validation failed: Members match: ${membersMatch}, Scenario match: ${scenarioMatch}`);
      }

    } catch (error) {
      setScenarios(prev => prev.map(s =>
        s.id === scenarioId ? {
          ...s,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        } : s
      ));
    }
  };

  const runAllScenarios = async () => {
    for (const scenario of scenarios) {
      await runScenario(scenario.id);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'running': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  if (!user || profile?.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-red-600">This test suite requires admin access.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>🧪 Internal Task Chat Membership Tester</CardTitle>
        <div className="flex gap-2">
          <Button onClick={runAllScenarios} size="sm">
            Run All Tests
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {scenarios.map((scenario) => (
            <div key={scenario.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(scenario.status)}>
                    {scenario.status}
                  </Badge>
                  <h3 className="font-medium">{scenario.name}</h3>
                </div>
                <Button
                  onClick={() => runScenario(scenario.id)}
                  size="sm"
                  disabled={scenario.status === 'running'}
                >
                  {scenario.status === 'running' ? 'Running...' : 'Run Test'}
                </Button>
              </div>

              <p className="text-sm text-gray-600 mb-2">{scenario.description}</p>

              {scenario.result && (
                <div className="text-xs bg-green-50 p-2 rounded">
                  <p><strong>Task ID:</strong> {scenario.result.taskId}</p>
                  <p><strong>Expected Members:</strong> {scenario.result.expectedMembers.join(', ')}</p>
                  <p><strong>Actual Members:</strong> {scenario.result.actualMembers.join(', ')}</p>
                  <p><strong>Scenario:</strong> {scenario.result.actualScenario}</p>
                  <p><strong>Description:</strong> {scenario.result.description}</p>
                </div>
              )}

              {scenario.error && (
                <div className="text-xs bg-red-50 p-2 rounded text-red-700">
                  <strong>Error:</strong> {scenario.error}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default InternalTaskChatTester;
