/**
 * TaskActions Component
 *
 * This component implements the "always mount, conditionally render" pattern.
 * It renders the appropriate actions based on the task type and user role.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, ClipboardCheck } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Task, isInternalTask, isExternalTask } from '@/types/tasks';
import { isInRoleGroup } from '@/constants/roles';
import { ROLE_GROUPS } from '@/constants/roles';

// Import action components
import InternalTaskActions from './InternalTaskActions';
import SupplierActions from './SupplierActions';
import TaskCompletionActions from './TaskCompletionActions';
import TaskOwnerOfferActions from './TaskOwnerOfferActions';
import AdminReviewStatus from './AdminReviewStatus';

interface TaskActionsProps {
  task: Task | null;
  isLoading: boolean;
  error?: Error | null;
  userOffer?: any;
  acceptedOffer?: any;
  onRequestInfo?: () => void;
  onTaskUpdated?: () => void;
}

/**
 * TaskActions component that always mounts but conditionally renders content
 */
const TaskActions: React.FC<TaskActionsProps> = ({
  task,
  isLoading,
  error,
  userOffer,
  acceptedOffer,
  onRequestInfo,
  onTaskUpdated = () => {}
}) => {
  const { user, profile, userRole } = useAuth();

  // Early return for loading state
  if (isLoading) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-72" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-10 w-full mb-2" />
          <Skeleton className="h-10 w-full" />
        </CardContent>
      </Card>
    );
  }

  // Early return for error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load task actions: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  // Early return if no task or user
  if (!task || !user || !profile) {
    return null;
  }

  // Determine user relationship to task
  const isTaskOwner = task.user_id === user.id;
  const isAssignedToUser = String(task.assigned_to) === String(user.id);
  const isAdmin = userRole === 'admin';
  const isSupplier = profile.account_type === 'supplier';
  const isMaintenance = userRole === 'maintenance';
  const isAssignableStaff = isInRoleGroup(userRole, ROLE_GROUPS.TASK_ASSIGNABLE);

  // Determine the most appropriate action component to show
  // We'll only show one action component to simplify the UI
  const getActionComponent = () => {
    console.log('TaskActions.getActionComponent called for task:', {
      id: task.id,
      status: task.status,
      type: task.type,
      offersCount: task.offers_count,
      isAdmin,
      isTaskOwner,
      isSupplier,
      isInternalTask: isInternalTask(task),
      isExternalTask: isExternalTask(task)
    });
    // For internal tasks
    if (isInternalTask(task)) {
      if (isAdmin || isTaskOwner || (isAssignableStaff && isAssignedToUser)) {
        return {
          title: isAssignedToUser ? 'Your Task Actions' : 'Manage Internal Task',
          description: isAssignedToUser
            ? 'Update the status of your assigned task'
            : 'Manage this internal task',
          component: <InternalTaskActions task={task} onTaskUpdated={onTaskUpdated} />,
          borderColor: 'border-blue-300'
        };
      }
    }

    // Special case for teachers viewing their own tasks with 'admin' visibility
    if (userRole === 'teacher' && isTaskOwner && task.visibility === 'admin') {
      console.log('TaskActions: Teacher viewing their own task pending admin review');
      return {
        title: 'Awaiting Review',
        description: 'This task is awaiting review by a school administrator',
        component: <AdminReviewStatus task={task} />,
        borderColor: 'border-amber-300'
      };
    }

    // For external tasks
    if (isExternalTask(task)) {
      console.log('TaskActions: Handling external task with status:', task.status, 'and visibility:', task.visibility);

      // Supplier viewing a public task
      if (isSupplier && task.visibility === 'public') {
        console.log('TaskActions: Showing supplier actions for external task');
        return {
          title: 'Supplier Actions',
          description: 'Submit an offer or manage this task',
          component: (
            <SupplierActions
              task={task}
              existingOffer={userOffer}
              showRequestInfo={false}
              onRequestInfo={onRequestInfo || (() => {})}
            />
          ),
          borderColor: 'border-amber-300'
        };
      }

      // Task owner or admin viewing a public task
      if ((isTaskOwner || isAdmin) && task.visibility === 'public') {
        console.log('TaskActions: Admin/owner viewing public external task with status:', task.status);
        // If task is completed or confirmed, show completion actions
        if (task.status === 'completed' || task.status === 'confirmed') {
          return {
            title: 'Completion Actions',
            description: 'Finalize this task',
            component: (
              <TaskCompletionActions
                task={task}
                acceptedOffer={acceptedOffer}
                onTaskUpdated={onTaskUpdated}
              />
            ),
            borderColor: 'border-purple-300'
          };
        }

        // For tasks with offers or in offer-related statuses, show offer management
        if (task.status === 'questions' || task.status === 'interest' || task.status === 'offer' ||
            task.offers_count > 0) {
          console.log('TaskActions: Showing offer management for task with status:', task.status, 'and offers_count:', task.offers_count);

          try {
            return {
              title: 'Offer Management',
              description: 'Review and manage offers for this task',
              component: (
                <TaskOwnerOfferActions
                  task={task}
                  onTaskUpdated={onTaskUpdated}
                />
              ),
              borderColor: 'border-yellow-300'
            };
          } catch (error) {
            console.error('Error creating TaskOwnerOfferActions component:', error);
            return {
              title: 'Error Loading Offers',
              description: 'There was an error loading the offers for this task',
              component: (
                <div className="p-4 text-red-500">
                  Error loading offers. Please try refreshing the page.
                </div>
              ),
              borderColor: 'border-red-300'
            };
          }
        }

        // Otherwise show task owner actions
        return {
          title: 'Task Management',
          description: 'Manage task status and visibility',
          component: (
            <TaskOwnerOfferActions
              task={task}
              onTaskUpdated={onTaskUpdated}
            />
          ),
          borderColor: 'border-green-300'
        };
      }
    }

    // Admin review case - tasks pending admin review
    if (isAdmin && task.visibility === 'admin' && task.status === 'open') {
      console.log('TaskActions: Admin viewing task pending review');
      return {
        title: 'Admin Review Required',
        description: 'This task needs to be reviewed and assigned',
        component: (
          <div className="space-y-4">
            <p className="text-amber-700 bg-amber-50 p-3 rounded-md border border-amber-200">
              This task has been submitted by a teacher and requires admin review before assignment.
            </p>
            <Button
              onClick={() => window.location.href = 'https://classtasker.com/admin/task-review'}
              className="w-full bg-classtasker-blue hover:bg-classtasker-blue/90"
            >
              <ClipboardCheck className="h-4 w-4 mr-2" />
              Review & Assign Task
            </Button>
          </div>
        ),
        borderColor: 'border-amber-300'
      };
    }

    // Default case - no actions available
    return {
      title: 'Task Information',
      description: 'View-only mode',
      component: (
        <p className="text-gray-500">
          You don't have any available actions for this task.
        </p>
      ),
      borderColor: 'border-gray-300'
    };
  };

  const actionInfo = getActionComponent();

  // Render the appropriate action component
  return (
    <Card className={`mb-6 border-2 ${actionInfo.borderColor}`}>
      <CardHeader>
        <CardTitle>{actionInfo.title}</CardTitle>
        <CardDescription>{actionInfo.description}</CardDescription>
      </CardHeader>
      <CardContent>
        {actionInfo.component}
      </CardContent>
    </Card>
  );
};

export default TaskActions;
