<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <title>ClassTasker Connect</title>
    <meta name="description" content="School maintenance and task management platform" />
    <!-- Force deployment update - Cache bust v2025-01-25 -->
    <meta name="author" content="ClassTasker" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/icons/placeholder.svg" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="ClassTasker Connect" />
    <meta property="og:description" content="School maintenance and task management platform" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://classtasker.com/opengraph-image.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="ClassTasker" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.svg" />
    <link rel="icon" href="/icons/icon-192x192.svg" type="image/svg+xml" />

    <!-- Environment Variables -->
    <script src="/env-config.js?v=2025-01-25"></script>
    <script>
      // SECURITY: Fallback environment variables - these should be replaced by env-config.js
      // NEVER hardcode actual keys here in production
      window.env = window.env || {
        VITE_SUPABASE_URL: "https://qcnotlojmyvpqbbgoxbc.supabase.co",
        VITE_SUPABASE_ANON_KEY: "PLACEHOLDER_ANON_KEY_SET_IN_ENV_CONFIG"
      };
    </script>

  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>

    <!-- PWA Installation and Service Worker Registration -->
    <script>
      // Initialize global variable to store the installation prompt
      window.deferredPrompt = null;

      // Listen for beforeinstallprompt event
      window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 76+ from automatically showing the prompt
        e.preventDefault();
        // Store the event so it can be triggered later
        window.deferredPrompt = e;
        console.log('beforeinstallprompt event fired and stored globally');
      });

      // Listen for appinstalled event
      window.addEventListener('appinstalled', (e) => {
        // Clear the deferredPrompt variable
        window.deferredPrompt = null;
        console.log('PWA was installed');
      });

      // Register service worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
              console.log('ServiceWorker registration successful with scope: ', registration.scope);
            })
            .catch(error => {
              console.log('ServiceWorker registration failed: ', error);
            });
        });
      }
    </script>
  </body>
</html>
