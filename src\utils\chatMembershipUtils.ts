/**
 * Chat Membership Utilities for ClassTasker
 *
 * This module provides centralized logic for determining chat membership
 * across all task scenarios to ensure consistency and avoid missing members.
 */

import { supabase } from '@/integrations/supabase/client';

export interface TaskChatContext {
  taskId: string;
  creatorId: string;
  assignerId?: string;
  assigneeId?: string;
  taskStatus: string;
  visibility: 'admin' | 'internal' | 'public';
}

export interface ChatMembershipResult {
  members: string[];
  scenario: string;
  description: string;
}

/**
 * Determines chat members for a task based on the comprehensive scenario matrix
 *
 * Scenario Matrix:
 * 1. Admin Creates & Self-Assigns (1 member): Admin only
 * 2. Admin Creates & Assigns to Internal Staff (2 members): Admin + Staff
 * 3. Admin Creates & Assigns to Supplier (2 members): Admin + Supplier
 * 4. Teacher Creates, Admin Self-Assigns (2 members): Teacher + Admin
 * 5. Teacher Creates, Admin Assigns to Internal Staff (3 members): Teacher + Admin + Staff
 * 6. Teacher Creates, Admin Assigns to Supplier (3 members): Teacher + Admin + Supplier
 */
export async function determineTaskChatMembers(context: TaskChatContext): Promise<ChatMembershipResult> {
  const { taskId, creatorId, assignerId, assigneeId, taskStatus, visibility } = context;

  // Initialize members array
  const members: string[] = [];
  let scenario = '';
  let description = '';

  // Always include the task creator
  if (creatorId) {
    members.push(creatorId);
  }

  // Determine scenario based on creator, assigner, and assignee relationships
  const isCreatorAdmin = await isUserAdmin(creatorId);
  const isAssignerAdmin = assignerId ? await isUserAdmin(assignerId) : false;
  const isAssigneeAdmin = assigneeId ? await isUserAdmin(assigneeId) : false;
  const isAssigneeSupplier = assigneeId ? await isUserSupplier(assigneeId) : false;

  if (isCreatorAdmin) {
    // Admin created the task
    if (!assigneeId) {
      // No assignment yet - just admin
      scenario = 'admin_created_unassigned';
      description = 'Admin created task, not yet assigned';
    } else if (assigneeId === creatorId) {
      // Admin self-assigned
      scenario = 'admin_self_assigned';
      description = 'Admin created and self-assigned task';
      // Only admin is needed (already added as creator)
    } else if (isAssigneeSupplier) {
      // Admin assigned to supplier
      scenario = 'admin_assigned_to_supplier';
      description = 'Admin created and assigned to supplier';
      if (!members.includes(assigneeId)) {
        members.push(assigneeId);
      }
    } else {
      // Admin assigned to internal staff
      scenario = 'admin_assigned_to_staff';
      description = 'Admin created and assigned to internal staff';
      if (!members.includes(assigneeId)) {
        members.push(assigneeId);
      }
    }
  } else {
    // Teacher created the task
    if (!assigneeId || !assignerId) {
      // No assignment yet - just teacher (awaiting admin review)
      scenario = 'teacher_created_pending_review';
      description = 'Teacher created task, awaiting admin review';
    } else if (assigneeId === assignerId) {
      // Teacher created, admin self-assigned
      scenario = 'teacher_created_admin_self_assigned';
      description = 'Teacher created task, admin self-assigned';
      if (assignerId && !members.includes(assignerId)) {
        members.push(assignerId);
      }
    } else if (isAssigneeSupplier) {
      // Teacher created, admin assigned to supplier
      scenario = 'teacher_created_admin_assigned_to_supplier';
      description = 'Teacher created task, admin assigned to supplier';
      if (assignerId && !members.includes(assignerId)) {
        members.push(assignerId);
      }
      if (assigneeId && !members.includes(assigneeId)) {
        members.push(assigneeId);
      }
    } else {
      // Teacher created, admin assigned to internal staff
      scenario = 'teacher_created_admin_assigned_to_staff';
      description = 'Teacher created task, admin assigned to internal staff';
      if (assignerId && !members.includes(assignerId)) {
        members.push(assignerId);
      }
      if (assigneeId && !members.includes(assigneeId)) {
        members.push(assigneeId);
      }
    }
  }

  // Remove any undefined or null values and ensure uniqueness
  const cleanMembers = [...new Set(members.filter(Boolean))];

  console.log(`[ChatMembership] Task ${taskId} - Scenario: ${scenario}`);
  console.log(`[ChatMembership] Members: ${cleanMembers.join(', ')}`);
  console.log(`[ChatMembership] Description: ${description}`);

  return {
    members: cleanMembers,
    scenario,
    description
  };
}

/**
 * Helper function to check if a user is an admin
 */
async function isUserAdmin(userId: string): Promise<boolean> {
  try {
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    return profile?.role === 'admin';
  } catch (error) {
    console.error(`[ChatMembership] Error checking if user ${userId} is admin:`, error);
    return false;
  }
}

/**
 * Helper function to check if a user is a supplier
 */
async function isUserSupplier(userId: string): Promise<boolean> {
  try {
    const { data: profile } = await supabase
      .from('profiles')
      .select('account_type')
      .eq('id', userId)
      .single();

    return profile?.account_type === 'supplier';
  } catch (error) {
    console.error(`[ChatMembership] Error checking if user ${userId} is supplier:`, error);
    return false;
  }
}

/**
 * Gets task context information needed for chat membership determination
 */
export async function getTaskChatContext(taskId: string): Promise<TaskChatContext | null> {
  try {
    const { data: task, error } = await supabase
      .from('tasks')
      .select('id, user_id, assigned_to, status, visibility')
      .eq('id', taskId)
      .single();

    if (error || !task) {
      console.error(`[ChatMembership] Error fetching task ${taskId}:`, error);
      return null;
    }

    // Determine the assigner based on the task context
    // If the task has an assignee, we need to determine who did the assignment
    let assignerId: string | undefined;

    if (task.assigned_to) {
      // Check if the creator is an admin
      const isCreatorAdmin = await isUserAdmin(task.user_id);

      if (isCreatorAdmin) {
        // Admin created and assigned the task
        assignerId = task.user_id;
      } else {
        // Teacher created the task, so an admin must have assigned it
        // We'll need to find which admin assigned it
        // For now, we'll use a heuristic: if the task is assigned, assume an admin did it
        // In a future enhancement, we could add an 'assigned_by' field to track this explicitly
        assignerId = await findAssigningAdmin(taskId, task.user_id);
      }
    }

    return {
      taskId: task.id,
      creatorId: task.user_id,
      assignerId,
      assigneeId: task.assigned_to,
      taskStatus: task.status,
      visibility: task.visibility
    };
  } catch (error) {
    console.error(`[ChatMembership] Error getting task context for ${taskId}:`, error);
    return null;
  }
}

/**
 * Helper function to find the admin who assigned a teacher's task
 * This is a heuristic approach until we add explicit assignment tracking
 */
async function findAssigningAdmin(taskId: string, creatorId: string): Promise<string | undefined> {
  try {
    // For now, we'll return the first admin in the same organization as the creator
    // This is a simplification - in practice, you might want to track this more explicitly

    // Get the creator's organization
    const { data: creatorProfile } = await supabase
      .from('profiles')
      .select('organization_id')
      .eq('id', creatorId)
      .single();

    if (!creatorProfile?.organization_id) {
      return undefined;
    }

    // Find an admin in the same organization
    const { data: adminProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('organization_id', creatorProfile.organization_id)
      .eq('role', 'admin')
      .limit(1)
      .single();

    return adminProfile?.id;
  } catch (error) {
    console.error(`[ChatMembership] Error finding assigning admin for task ${taskId}:`, error);
    return undefined;
  }
}

/**
 * Main function to get chat members for a task
 * This is the primary function that should be used throughout the application
 */
export async function getTaskChatMembers(taskId: string): Promise<string[]> {
  try {
    const context = await getTaskChatContext(taskId);
    if (!context) {
      console.error(`[ChatMembership] Could not get context for task ${taskId}`);
      return [];
    }

    const result = await determineTaskChatMembers(context);
    return result.members;
  } catch (error) {
    console.error(`[ChatMembership] Error determining chat members for task ${taskId}:`, error);
    return [];
  }
}

/**
 * Updates chat membership when task assignment changes
 */
export async function updateTaskChatMembership(
  taskId: string,
  newAssigneeId?: string,
  assignerId?: string
): Promise<boolean> {
  try {
    console.log(`[ChatMembership] Updating chat membership for task ${taskId}`);
    console.log(`[ChatMembership] Parameters: newAssigneeId=${newAssigneeId}, assignerId=${assignerId}`);

    // Get updated task context
    const context = await getTaskChatContext(taskId);
    if (!context) {
      console.error(`[ChatMembership] Failed to get task context for ${taskId}`);
      return false;
    }

    console.log(`[ChatMembership] Original context:`, {
      taskId: context.taskId,
      creatorId: context.creatorId,
      assignerId: context.assignerId,
      assigneeId: context.assigneeId,
      taskStatus: context.taskStatus,
      visibility: context.visibility
    });

    // Update context with new assignment info if provided
    if (newAssigneeId !== undefined) {
      context.assigneeId = newAssigneeId;
      console.log(`[ChatMembership] Updated assigneeId to: ${newAssigneeId}`);
    }
    if (assignerId) {
      context.assignerId = assignerId;
      console.log(`[ChatMembership] Updated assignerId to: ${assignerId}`);
    }

    // Determine new membership
    const result = await determineTaskChatMembers(context);
    console.log(`[ChatMembership] Determined membership result:`, result);

    // Update the GetStream channel membership
    const channelId = `task-${taskId}`;

    // Call the server endpoint to update channel membership
    console.log(`[ChatMembership] Calling GetStream API to update channel ${channelId} with members:`, result.members);
    const response = await fetch('/api/getstream/channels', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskId,
        taskTitle: `Task ${taskId}`,
        members: result.members
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[ChatMembership] GetStream API error: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`Failed to update channel membership: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log(`[ChatMembership] GetStream API response:`, responseData);

    console.log(`[ChatMembership] Successfully updated membership for task ${taskId}`);
    console.log(`[ChatMembership] New members: ${result.members.join(', ')}`);
    console.log(`[ChatMembership] Scenario: ${result.scenario} - ${result.description}`);

    return true;
  } catch (error) {
    console.error(`[ChatMembership] Error updating chat membership for task ${taskId}:`, error);
    return false;
  }
}

/**
 * Creates or updates a task chat with proper membership
 * This function should be called whenever a task is created or its assignment changes
 */
export async function ensureTaskChatExists(taskId: string, taskTitle?: string): Promise<boolean> {
  try {
    console.log(`[ChatMembership] Ensuring chat exists for task ${taskId}`);

    // Get the correct members for this task
    const members = await getTaskChatMembers(taskId);

    if (members.length === 0) {
      console.warn(`[ChatMembership] No members determined for task ${taskId}`);
      return false;
    }

    // Get task title if not provided
    let title = taskTitle;
    if (!title) {
      const { data: task } = await supabase
        .from('tasks')
        .select('title')
        .eq('id', taskId)
        .single();
      title = task?.title || `Task ${taskId}`;
    }

    // Create or update the channel
    const response = await fetch('/api/getstream/channels', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskId,
        taskTitle: title,
        members
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to create/update channel');
    }

    console.log(`[ChatMembership] Successfully ensured chat exists for task ${taskId}`);
    console.log(`[ChatMembership] Members: ${members.join(', ')}`);

    return true;
  } catch (error) {
    console.error(`[ChatMembership] Error ensuring chat exists for task ${taskId}:`, error);
    return false;
  }
}
