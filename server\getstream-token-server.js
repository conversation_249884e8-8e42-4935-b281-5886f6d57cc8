/**
 * GetStream Token Server
 *
 * A simple Express server for generating GetStream tokens and managing channels.
 * Run with: node server/getstream-token-server.js
 */

import express from 'express';
import cors from 'cors';
import { StreamChat } from 'stream-chat';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get API key and secret from environment variables
const apiKey = process.env.GETSTREAM_API_KEY;
const apiSecret = process.env.GETSTREAM_API_SECRET;

// Check if API key and secret are available
if (!apiKey || !apiSecret) {
  console.error('Error: GetStream API key or secret is missing in environment variables.');
  console.error('Make sure GETSTREAM_API_KEY and GETSTREAM_API_SECRET are set in your .env file.');
  process.exit(1);
}

// Create a server-side client for GetStream
const serverClient = StreamChat.getInstance(apiKey, apiSecret);

// Create an Express app
const app = express();
const PORT = 3002;

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Add OPTIONS handling for preflight requests
app.options('/*', cors());

// Parse JSON request bodies
app.use(express.json());

// Generate a token for a user (old endpoint)
app.post('/token', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user:', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

// Create a channel for a task
app.post('/channels', async (req, res) => {
  try {
    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Creating channel for task:', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    try {
      // First try to get the channel if it exists
      const channel = serverClient.channel('messaging', channelId);
      await channel.query();

      // If channel exists, update members by replacing the entire member list
      console.log('Channel already exists, updating membership:', channelId);

      // Get current members
      const currentMembers = Object.keys(channel.state?.members || {});
      console.log(`Current members: ${currentMembers.join(', ')}`);
      console.log(`Target members: ${channelMembers.join(', ')}`);

      // Find members to remove (current members not in target list)
      const membersToRemove = currentMembers.filter(member => !channelMembers.includes(member));

      // Find members to add (target members not in current list)
      const membersToAdd = channelMembers.filter(member => !currentMembers.includes(member));

      // Remove members who should no longer have access
      if (membersToRemove.length > 0) {
        await channel.removeMembers(membersToRemove);
        console.log(`Removed ${membersToRemove.length} members from channel:`, membersToRemove.join(', '));
      }

      // Add new members
      if (membersToAdd.length > 0) {
        await channel.addMembers(membersToAdd);
        console.log(`Added ${membersToAdd.length} members to channel:`, membersToAdd.join(', '));
      }

      if (membersToRemove.length === 0 && membersToAdd.length === 0) {
        console.log('No membership changes needed for channel:', channelId);
      }

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'updated'
      });
    } catch (error) {
      // If channel doesn't exist, create it
      console.log('Channel does not exist, creating new channel');

      // Create a new channel
      const channel = serverClient.channel('messaging', channelId, {
        name: taskTitle,
        members: channelMembers,
        task_id: taskId
      });

      await channel.create();

      console.log('Channel created successfully:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'created'
      });
    }
  } catch (error) {
    console.error('Error creating/updating channel:', error);
    res.status(500).json({ error: 'Failed to create/update channel' });
  }
});

// Get a channel
app.get('/channels/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;

    if (!channelId) {
      return res.status(400).json({ error: 'Channel ID is required' });
    }

    console.log('Getting channel:', channelId);

    try {
      // Try to get the channel
      const channel = serverClient.channel('messaging', channelId);
      const state = await channel.query();

      console.log('Channel found:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: state.members,
        status: 'found'
      });
    } catch (error) {
      console.error('Error getting channel:', error);
      res.status(404).json({ error: 'Channel not found' });
    }
  } catch (error) {
    console.error('Error getting channel:', error);
    res.status(500).json({ error: 'Failed to get channel' });
  }
});

// Add members to a channel
app.post('/channels/:channelId/members', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { members } = req.body;

    if (!channelId) {
      return res.status(400).json({ error: 'Channel ID is required' });
    }

    if (!members || !Array.isArray(members) || members.length === 0) {
      return res.status(400).json({ error: 'Members array is required' });
    }

    console.log(`Adding ${members.length} members to channel:`, channelId);

    try {
      // Get the channel
      const channel = serverClient.channel('messaging', channelId);

      // Add members to the channel
      const result = await channel.addMembers(members);

      console.log('Members added successfully to channel:', channelId);

      res.json({
        channelId,
        members: result.members,
        status: 'updated'
      });
    } catch (error) {
      console.error('Error adding members to channel:', error);
      res.status(500).json({ error: 'Failed to add members to channel' });
    }
  } catch (error) {
    console.error('Error adding members to channel:', error);
    res.status(500).json({ error: 'Failed to add members to channel' });
  }
});

// Send a system message to a channel
app.post('/system-message', async (req, res) => {
  try {
    const { channelId, text } = req.body;

    if (!channelId || !text) {
      return res.status(400).json({ error: 'Channel ID and text are required' });
    }

    console.log('Sending system message to channel:', channelId);

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Send the system message
    const message = await channel.sendMessage({
      text,
      type: 'system',
      user_id: 'system',
    });

    console.log('System message sent successfully:', message);

    res.json({ success: true, message });
  } catch (error) {
    console.error('Error sending system message:', error);
    res.status(500).json({ error: 'Failed to send system message' });
  }
});

// API Routes for Vercel Deployment

// Generate a token for a user (new endpoint)
app.post('/api/getstream/token', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user (new endpoint):', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

// Create a channel for a task (new endpoint)
app.post('/api/getstream/channels', async (req, res) => {
  try {
    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Creating channel for task (new endpoint):', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    try {
      // First try to get the channel if it exists
      const channel = serverClient.channel('messaging', channelId);
      await channel.query();

      // If channel exists, update members
      if (channelMembers.length > 0) {
        // Add members to the channel
        await channel.addMembers(channelMembers);
        console.log(`Added ${channelMembers.length} members to existing channel:`, channelId);
      }

      console.log('Channel already exists:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'updated'
      });
    } catch (error) {
      // If channel doesn't exist, create it
      console.log('Channel does not exist, creating new channel');

      // Create a new channel
      const channel = serverClient.channel('messaging', channelId, {
        name: taskTitle,
        members: channelMembers,
        task_id: taskId
      });

      await channel.create();

      console.log('Channel created successfully:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'created'
      });
    }
  } catch (error) {
    console.error('Error creating/updating channel:', error);
    res.status(500).json({ error: 'Failed to create/update channel' });
  }
});

// Send a system message to a channel (new endpoint)
app.post('/api/getstream/system-message', async (req, res) => {
  try {
    const { channelId, text } = req.body;

    if (!channelId || !text) {
      return res.status(400).json({ error: 'Channel ID and text are required' });
    }

    console.log('Sending system message to channel (new endpoint):', channelId);

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Send the system message
    const message = await channel.sendMessage({
      text,
      type: 'system',
      user_id: 'system',
    });

    console.log('System message sent successfully:', message);

    res.json({ success: true, message });
  } catch (error) {
    console.error('Error sending system message:', error);
    res.status(500).json({ error: 'Failed to send system message' });
  }
});

// Simple route for testing
app.get('/', (req, res) => {
  res.json({ status: 'GetStream Token Server is running' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`GetStream Token Server listening on port ${PORT}`);
  console.log(`API Key: ${apiKey}`);
  console.log(`API Secret: ${apiSecret.substring(0, 5)}...${apiSecret.substring(apiSecret.length - 5)}`);
});
